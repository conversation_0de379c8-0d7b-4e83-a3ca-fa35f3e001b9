import csv
import os
import sys

def read_usernames(filename: str) -> set:
    """
    Liest die CSV-Datei `filename` ein und gibt ein Set aller Einträge
    in der Spalte 'username' zurück (case-insensitive, trimmt Leerzeichen).
    Gibt leeres Set zurück, wenn keine passende Spalte gefunden wird.
    """
    usernames = set()
    with open(filename, mode='r', encoding='utf-8', newline='') as f:
        reader = csv.DictReader(f)
        if reader.fieldnames is None:
            print(f"Warnung: Datei '{filename}' scheint keine Kopfzeile zu haben.")
            return usernames

        # Alle Header trimmen und in Kleinbuchstaben umwandeln
        normalized = {fn: fn.strip().lower() for fn in reader.fieldnames}
        # Versuchen, die Spalte 'username' zu finden
        target_field = None
        for original, norm in normalized.items():
            if norm == 'username':
                target_field = original
                break

        if not target_field:
            print(f"Warnung: Datei '{filename}' enthält keine Spalte 'username'. Gefundene Header:")
            print(f"  {reader.fieldnames}")
            return usernames

        for row in reader:
            value = row.get(target_field, '').strip()
            if value:
                usernames.add(value)
    return usernames

def main():
    old_file = 'old.csv'
    new_file = 'new.csv'

    if not os.path.isfile(old_file):
        print(f"Fehler: '{old_file}' wurde nicht gefunden.")
        sys.exit(1)
    if not os.path.isfile(new_file):
        print(f"Fehler: '{new_file}' wurde nicht gefunden.")
        sys.exit(1)

    old_usernames = read_usernames(old_file)
    new_usernames = read_usernames(new_file)

    missing_in_new = old_usernames - new_usernames

    if missing_in_new:
        print("Usernames in old.csv, die NICHT in new.csv vorkommen:")
        for username in sorted(missing_in_new):
            print(username)
    else:
        print("Keine Usernames aus old.csv fehlen in new.csv.")

if __name__ == '__main__':
    main()
